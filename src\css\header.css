.header {
  padding: 20px 0;
}
.nav {
  display: flex;
  justify-content: space-between;
}
.nav-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.nav-list {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 40px;
}
.nav-link {
  position: relative;
  display: block;
  font-weight: 500;
  letter-spacing: 0.02em;
  transition: 200ms ease-in;
}
.nav-link:hover,
.nav-link:focus {
  color: #747bff;
}
.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(100%);
  width: 110%;
  height: 4px;
  border-radius: 4px;
  transform-origin: center;
  animation: stretch 400ms cubic-bezier(0.68, -0.55, 0.27, 1.55) 200ms both;
}
.nav-link.active::after {
  background: #747bff;
}
