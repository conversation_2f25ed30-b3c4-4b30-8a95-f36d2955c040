.vite-promo {
  padding: 50px 0;
}
.vite-promo-thumb {
  position: relative;
  max-width: 320px;
  margin: 0 auto;
}
.vite-promo-thumb::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;

  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-image: linear-gradient(-45deg, #bd34fe 50%, #47caff 50%);
  filter: blur(4.5rem);
}
.main-title {
  text-align: center;
}
.main-title-gradient {
  display: block;
  font-size: 2em;
  background-image: linear-gradient(120deg, #bd34fe 30%, #41d1ff);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}
@media only screen and (min-width: 768px) {
  .main-title-gradient {
    font-size: 3em;
  }
}
.main-title-link {
  position: relative;
  letter-spacing: 0.04em;
}
.main-title-link::before {
  content: '';
  position: absolute;
  bottom: 4%;
  left: 50%;
  transform: translateX(-50%) skewX(-12deg) rotate(-2deg);
  z-index: -1;
  width: 108%;
  height: 24%;
  background-color: #41d1ff;
  transition: 200ms ease;
}
.main-title-link:hover::before,
.main-title-link:focus::before {
  background-color: #ff6b0a;
}

.vite-promo .title,
.vite-promo .text {
  margin-bottom: 1.5rem;

  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.14;

  color: #213547;
}

.vite-promo .tagline {
  font-size: 1.5rem;
  line-height: 1.5;
  color: rgba(60, 60, 60, 0.7);
}

.vite-promo .actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.vite-promo .link {
  display: inline-block;
  padding: 0.5rem 1.25rem;

  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  color: rgba(255, 255, 255, 0.87);

  background-color: #646cff;
  border: 1px solid transparent;
  border-radius: 1.25rem;
  transition: color 0.25s, border-color 0.25s, background-color 0.25s;
}
